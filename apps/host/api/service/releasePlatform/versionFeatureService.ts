import { VersionFeatureInfo } from '@shared/releasePlatform/versionFeatureInfo';
import { Inject, Injectable } from '@gulux/gulux';
import VersionFeatureInfoDao from '../dao/releasePlatform/versionFeatureInfoDao';
import MeegoService from '../third/meego';
import BitsService from '../third/bits';
import { MrMeegoInfo } from '@shared/bits/mrMeegoInfo';
import LarkService from '@pa/backend/dist/src/third/lark';
import { FieldValue, Roles } from '@shared/meego/MeegoCommon';
import { ChatInfo, User } from '@pa/shared/dist/src/core';
import { AppSettingId, BusinessAppInfo } from '@pa/shared/dist/src/appSettings/appSettings';
import versionUtils, { cc2lvVersion } from '../../utils/versionUtils';
import BusinessConfigService from '@pa/backend/dist/src/service/businessConfig';
import VersionReleaseCardService from './VersionReleaseCard';
import { VersionConfigKeys } from '@shared/aircraftConfiguration';
import AirplaneConfigService from '../AirplaneConfigService';
import MessageService from '@pa/backend/dist/src/service/message';
import { MsgCategory, MsgStrategy, MsgTemplate, MsgType } from '@pa/shared/dist/src/message';
import { UserIdType } from '@pa/shared/dist/src/lark/userInfo';
import VersionProcessInfoDao from '../dao/releasePlatform/VersionProcessInfoDao';
import { VersionStageStatus } from '@shared/releasePlatform/versionStage';
import RigService from '../third/rig';
import { RigSubmitCheckPRDInfo } from '@shared/rig';
import { CrucialRequirementService } from './CrucialRequirementService';
// import StoryRevenueTaskUpdateMeegoInfoService from '../../../../quality/api/service/storyRevenueReviewPlatform/StoryRevenueTaskUpdateMeegoInfoService';
import { compact } from 'lodash';
import { FaceUMeegoBusinessFieldInfo } from '@shared/releasePlatform/releasePlatformUtils';

@Injectable()
export default class VersionFeatureService {
  @Inject()
  private versionFeatureInfoDao: VersionFeatureInfoDao;
  @Inject()
  private meegoService: MeegoService;
  @Inject()
  private bitsService: BitsService;
  @Inject()
  private lark: LarkService;
  @Inject()
  private business: BusinessConfigService;
  @Inject()
  private versionReleaseCard: VersionReleaseCardService;
  @Inject()
  private paConfig: AirplaneConfigService;
  @Inject()
  private messageService: MessageService;
  @Inject()
  private versionInfoDao: VersionProcessInfoDao;
  @Inject()
  private rig: RigService;
  @Inject()
  private crucialRequirementService: CrucialRequirementService;
  // @Inject()
  // private storyRevenueTaskUpdateService: StoryRevenueTaskUpdateMeegoInfoService;

  async getVersionFeatureInfo(appId: number, version: string): Promise<VersionFeatureInfo[] | undefined> {
    const queryVersion = this.getQueryVersion(appId, version);
    const versionFeatureInfo = await this.versionFeatureInfoDao.list({
      appId,
      version: queryVersion,
    });
    if (!versionFeatureInfo) {
      return undefined;
    }
    return versionFeatureInfo;
  }

  async getCrucialFeatures(appId: number, version: string): Promise<VersionFeatureInfo[]> {
    const versionFeatures = await this.getVersionFeatureInfo(appId, version);
    if (!versionFeatures) {
      return [];
    }
    const crucialStories: VersionFeatureInfo[] = [];
    for (const versionFeature of versionFeatures) {
      const isCrucial = await this.crucialRequirementService.isCrucialStory(Number(versionFeature.meegoId));
      if (isCrucial) {
        crucialStories.push(versionFeature);
      } else if (versionFeature.isCrucial) {
        crucialStories.push(versionFeature);
      }
    }
    return crucialStories;
  }

  async refreshVersionFeatureInfo() {
    const appIds = [
      AppSettingId.LV_IOS,
      AppSettingId.LV_ANDROID,
      AppSettingId.LV_WIN,
      AppSettingId.LV_MAC,
      AppSettingId.RETOUCH_IOS,
      AppSettingId.RETOUCH_ANDROID,
    ];
    for (const appId of appIds) {
      const pnProgressVersions = await this.versionInfoDao.findOnProgressVersions(appId);
      if (!pnProgressVersions) {
        continue;
      }
      for (const version of pnProgressVersions) {
        if (version.version_stages[0] && version.version_stages[0].status === VersionStageStatus.Complete) {
          continue;
        }
        await this.updateVersionFeatureInfo(appId, version.version);
      }
    }
  }

  async updateRigVersionSubmitPrdDocs() {
    const appIds = [
      AppSettingId.LV_IOS,
      AppSettingId.CC_IOS,
      AppSettingId.LV_MAC,
      AppSettingId.CC_MAC,
      AppSettingId.HYPIC_IOS,
      AppSettingId.RETOUCH_IOS,
    ];
    for (const appId of appIds) {
      const onProgressVersions = await this.versionInfoDao.findOnProgressVersions(appId);
      if (!onProgressVersions) {
        continue;
      }
      const appInfo = await this.business.appID2AppInfo(appId);
      if (!appInfo) {
        continue;
      }
      for (const onProgressVersion of onProgressVersions) {
        const checkPrdInfo = {
          aid: appInfo.app_id,
          os: appInfo.platform,
          data: [],
        } as RigSubmitCheckPRDInfo;
        if (
          !onProgressVersion.version_stages[3] ||
          onProgressVersion.version_stages[3].status !== VersionStageStatus.NotStart
        ) {
          continue;
        }
        const featureInfos = await this.getVersionFeatureInfo(appId, onProgressVersion.version);
        if (!featureInfos) {
          continue;
        }
        for (const featureInfo of featureInfos) {
          if (featureInfo.prdUrl) {
            checkPrdInfo.data.push({
              version: onProgressVersion.version,
              meego_id: Number(featureInfo.meegoId),
              meego_url: featureInfo.meegoUrl,
              prd_url: featureInfo.prdUrl,
              prd_name: featureInfo.featureName,
            });
          }
        }
        await this.rig.updateVersionSubmitQualityPrdList(checkPrdInfo);
        await this.rig.applyVersionSubmitPrdPermission(
          onProgressVersion.app_id,
          onProgressVersion.version,
          appInfo.platform,
        );
      }
    }
  }

  async analyzeRigVersionSubmitPrd() {
    const appIds = [
      AppSettingId.LV_IOS,
      AppSettingId.CC_IOS,
      AppSettingId.LV_MAC,
      AppSettingId.CC_MAC,
      AppSettingId.HYPIC_IOS,
      AppSettingId.RETOUCH_IOS,
    ];
    for (const appId of appIds) {
      const appInfo = await this.business.appID2AppInfo(appId);
      if (!appInfo) {
        continue;
      }
      const onProgressVersions = await this.versionInfoDao.findOnProgressVersions(appId);
      if (!onProgressVersions) {
        continue;
      }
      for (const onProgressVersion of onProgressVersions) {
        if (
          !onProgressVersion.version_stages[0] ||
          onProgressVersion.version_stages[0].status !== VersionStageStatus.Complete
        ) {
          continue;
        }
        if (
          !onProgressVersion.version_stages[3] ||
          onProgressVersion.version_stages[3].status !== VersionStageStatus.NotStart
        ) {
          continue;
        }
        await this.analyzeSepcificVersionSubmitPrd(onProgressVersion.version, onProgressVersion.app_id);
      }
    }
  }

  async countFeatureByMeegoId(query: any) {
    const infos = await this.versionFeatureInfoDao.list(query);
    if (!infos) {
      return;
    }
    const countSet: Set<string> = new Set();
    for (const info of infos) {
      countSet.add(info.meegoId);
    }
    return countSet.size;
  }

  async analyzeSepcificVersionSubmitPrd(version: string, appId: number) {
    const chatId = '7444534019818029057';
    if (!chatId?.length) {
      return;
    }
    await this.rig.analyzeVersionSubmitPrd(appId, version, 'iOS', chatId);
    await this.rig.sendVersionPrdSubmitNoRiskCard(appId, version, 'iOS', chatId);
    await this.rig.sendVersionPrdSubmitApprovalCard(appId, version, 'iOS', chatId);
  }

  async updateVersionFeatureInfo(appId: number, version: string) {
    const appInfo = await this.business.appID2AppInfo(appId);
    if (!appInfo) {
      return;
    }
    const bitsId = this.getQueryBitsId(appInfo);
    const queryVersion = this.getQueryVersion(appInfo.app_id, version);
    let developBranch = 'rc/develop';
    if (appId === AppSettingId.RETOUCH_ANDROID || appId === AppSettingId.RETOUCH_IOS) {
      developBranch = 'develop';
    }
    const mrIds = await this.bitsService.getIntegrationMrIds(bitsId, queryVersion, developBranch);
    if (!mrIds.length) {
      return;
    }
    for (const mrId of mrIds) {
      await this.updateVersionFeatureInfoByMrId(appId, queryVersion, mrId);
    }
  }

  async clearVersionFeatureInfo(appId: number, version: string) {
    const queryVersion = this.getQueryVersion(appId, version);
    await this.versionFeatureInfoDao.deleteMany({
      appId,
      version: queryVersion,
    });
  }

  async updateVersionFeatureInfoByMrId(appId: number, version: string, mrId: number) {
    const appInfo = await this.business.appID2AppInfo(appId);
    if (!appInfo) {
      return;
    }
    const mrInfo = await this.bitsService.getMrInfo({
      mrId,
    });
    if (!mrInfo) {
      return;
    }
    const meegoInfoList: MrMeegoInfo[] = await this.bitsService.getBindMeegoTaskInfo({
      mr_id: mrId,
    });
    if (!meegoInfoList) {
      return;
    }
    const meegoInfos = meegoInfoList.filter(meegoInfo => meegoInfo.task_type === 'story');
    const meegoWork = await this.meegoService.requestTaskInfo('faceu', 'story', {
      work_item_ids: meegoInfos.map(meegoInfo => parseInt(meegoInfo.task_id, 10)),
    });
    if (meegoWork && meegoWork.data && Array.isArray(meegoWork.data) && meegoWork.data.length !== 0) {
      for (const meegoWorkItem of meegoWork.data) {
        const newVersionFeature = {
          appId,
          version,
          businessId: appInfo.business_id,
          meegoPriority: '',
          pmOwners: [],
          techOwners: [],
          mrId: mrInfo.id,
          mrUrl: mrInfo.mr_detail_url,
          meegoId: meegoWorkItem.id.toString(),
          featureName: meegoWorkItem.name,
          meegoUrl: meegoInfoList.find(meegoInfo => meegoInfo.task_id.toString() === meegoWorkItem.id.toString())
            ?.task_url,
        } as VersionFeatureInfo;
        const ownerField = meegoWorkItem.fields.find(value => value.field_key === 'role_owners');
        if (ownerField?.field_value) {
          const rolesJson = JSON.stringify(ownerField.field_value);
          const roles = JSON.parse(rolesJson) as Roles[];
          for (const role of roles) {
            if (role.owners && role.role === 'PM') {
              // 需求 PM
              newVersionFeature.pmOwners = await this.convertOwnersStringToUsers(role.owners);
            } else if (role.owners && role.role === 'role_501834') {
              // 技术 Owner
              newVersionFeature.techOwners = await this.convertOwnersStringToUsers(role.owners);
            }
          }
        }
        const priorityFeild = meegoWorkItem.fields.find(value => value.field_key === 'priority');
        if (priorityFeild?.field_value) {
          const level = JSON.stringify(priorityFeild.field_value);
          const file: FieldValue = JSON.parse(level);
          newVersionFeature.meegoPriority = file.label;
        }
        const prdFeild = meegoWorkItem.fields.find(value => value.field_key === 'storywiki');
        if (prdFeild?.field_value) {
          newVersionFeature.prdUrl = prdFeild.field_value as string;
        }
        const crucialFeild = meegoWorkItem.fields.find(value => value.field_key === 'field_012ab1');
        if (crucialFeild?.field_value) {
          newVersionFeature.isCrucial = Boolean(crucialFeild.field_value);
        }
        const businessFeild = meegoWorkItem.fields.find(value => value.field_key === 'business');
        const business_line_value = businessFeild?.field_value;
        if (typeof business_line_value === 'string') {
          const result = this.getMeegoBusinessLineLabel(business_line_value);
          const primary_business = result[0];
          const secondary_business = result[1];
          newVersionFeature.businessLine = compact([primary_business, secondary_business]).join('/');
        }
        const planVersionFields = meegoWorkItem.fields.find(value => value.field_key === 'planning_version');
        if ((planVersionFields?.field_value as number[])?.length) {
          const versionIdList: { versionId: number; versionName: string }[] = (await this.meegoService.queryVersionById(
            meegoWorkItem.project_key,
            planVersionFields?.field_value as number[],
          )) as {
            versionId: number;
            versionName: string;
          }[];
          for (const versionIdListElement of versionIdList) {
            if (appId === AppSettingId.LV_IOS || appId === AppSettingId.CC_IOS) {
              if (versionIdListElement.versionName.includes('剪映-iOS')) {
                newVersionFeature.appId = AppSettingId.LV_IOS;
                await this.versionFeatureInfoDao.create(newVersionFeature);
              }
              if (versionIdListElement.versionName.includes('CC-iOS')) {
                newVersionFeature.appId = AppSettingId.CC_IOS;
                await this.versionFeatureInfoDao.create(newVersionFeature);
              }
            }
            if (appId === AppSettingId.LV_ANDROID || appId === AppSettingId.CC_ANDROID) {
              if (versionIdListElement.versionName.includes('剪映-Android')) {
                newVersionFeature.appId = AppSettingId.LV_ANDROID;
                await this.versionFeatureInfoDao.create(newVersionFeature);
              }
              if (versionIdListElement.versionName.includes('CC-Android')) {
                newVersionFeature.appId = AppSettingId.CC_ANDROID;
                await this.versionFeatureInfoDao.create(newVersionFeature);
              }
            }
            if (appId === AppSettingId.LV_WIN) {
              if (versionIdListElement.versionName.includes('剪映专业版-Win')) {
                newVersionFeature.appId = AppSettingId.LV_WIN;
                await this.versionFeatureInfoDao.create(newVersionFeature);
              }
              if (versionIdListElement.versionName.includes('CC-PC-Win')) {
                newVersionFeature.appId = AppSettingId.CC_WIN;
                await this.versionFeatureInfoDao.create(newVersionFeature);
              }
            }
            if (appId === AppSettingId.LV_MAC) {
              if (versionIdListElement.versionName.includes('剪映专业版-Mac')) {
                newVersionFeature.appId = AppSettingId.LV_MAC;
                await this.versionFeatureInfoDao.create(newVersionFeature);
              }
              if (versionIdListElement.versionName.includes('CC-PC-Mac')) {
                newVersionFeature.appId = AppSettingId.CC_MAC;
                await this.versionFeatureInfoDao.create(newVersionFeature);
              }
            }
            if (appId === AppSettingId.RETOUCH_IOS) {
              if (versionIdListElement.versionName.includes('醒图-iOS')) {
                newVersionFeature.appId = AppSettingId.RETOUCH_IOS;
                await this.versionFeatureInfoDao.create(newVersionFeature);
              }
              if (versionIdListElement.versionName.includes('Hypic-iOS')) {
                newVersionFeature.appId = AppSettingId.HYPIC_IOS;
                await this.versionFeatureInfoDao.create(newVersionFeature);
              }
            }
            if (appId === AppSettingId.RETOUCH_ANDROID) {
              if (versionIdListElement.versionName.includes('醒图-Android')) {
                newVersionFeature.appId = AppSettingId.RETOUCH_ANDROID;
                await this.versionFeatureInfoDao.create(newVersionFeature);
              }
              if (versionIdListElement.versionName.includes('Hypic-Android')) {
                newVersionFeature.appId = AppSettingId.HYPIC_ANDROID;
                await this.versionFeatureInfoDao.create(newVersionFeature);
              }
            }
          }
        }
      }
    }
  }

  async sendVersionFeatureInfoCard(business: number, version: string) {
    const apps = await this.business.getAppList(business);
    const appIds = apps.map(app => app.app_id);
    const card = await this.versionReleaseCard.buildCCVersionFeatureInfoCard(version, [300601, 300602]);
    const chatInfo = (await this.paConfig.queryConfigItem(
      business.toString(),
      VersionConfigKeys.featureListBroadCastGroups,
    )) as ChatInfo;
    const testGroupId = await this.lark.chatId2OpenChatId('7241237046113632259');
    const messageObj = {
      name: '版本需求列表', // 消息分类描述, 例如"封版提醒上车消息"
      type: MsgType.GroupChat, // 消息类型：私聊还是群聊
      category: MsgCategory.Release, // 消息一级业务分类,尽量使用枚举
      subCategory: '', // 消息二级业务分类,尽量使用枚举
      strategy: MsgStrategy.Event, // 消息触发策略
      msgContent: card, // 消息内容
    } as MsgTemplate;
    if (chatInfo.chat_id) {
      await this.messageService.sendNormalMsg(messageObj, UserIdType.chatId, chatInfo.chat_id);
    }
  }

  /**
   * 给商业化广告子仓专门写的提醒分支拉出的方法
   */
  async sendUnifiedAdCheckOutInfo(version: string, isOverSea: boolean, isRetouch: boolean) {
    const unifiedAdloaderId = 536633;
    const chatId = 'oc_a9d9aa15814c2b0524ff0fbd1f9f05a4';

    const branchConfig = this.getBranchConfig(version, isOverSea, isRetouch);
    const latestRelease = await this.bitsService.searchBranch(unifiedAdloaderId, branchConfig.branchName);
    const branchExists = latestRelease.some(branchInfo => branchInfo.branch === branchConfig.branchName);

    const message = branchExists
      ? `Android子仓 unified_adloader ${branchConfig.branchName} 分支已随${branchConfig.productName}封版拉出`
      : `未找到unified_adloader ${branchConfig.branchName} 分支，请手动检查`;

    await this.lark.sendTextMessage(UserIdType.chatId, chatId, message);
  }

  /**
   * 根据参数获取分支配置信息
   */
  private getBranchConfig(version: string, isOverSea: boolean, isRetouch: boolean) {
    if (isRetouch) {
      return isOverSea
        ? { branchName: `release/hypic/${version}`, productName: 'hypic' }
        : { branchName: `release/retouch/${version}`, productName: '醒图' };
    } else {
      return isOverSea
        ? { branchName: `overseas/release/${version}`, productName: 'cc' }
        : { branchName: `release/${version}`, productName: '剪映' };
    }
  }

  async convertOwnersStringToUsers(owners: string[]): Promise<User[]> {
    const result: User[] = [];
    const meegoUserKeys: string[] = [];
    const emails: string[] = [];
    for (const owner of owners) {
      const tmpOwner = owner.trim().replace('@bytedance.com', '');
      const MEEGO_USER_KEY_PATTERN = /^\d{19}$/;
      if (MEEGO_USER_KEY_PATTERN.test(tmpOwner)) {
        // meego user key 形式
        meegoUserKeys.push(tmpOwner);
      } else {
        // 普通的英文名
        emails.push(`${tmpOwner}@bytedance.com`);
      }
    }
    // meego user key 形式，通过 Meego API 查询
    if (meegoUserKeys.length > 0) {
      const meegoUserInfoList = await this.meegoService.requestMeegoUserInfos({
        user_keys: meegoUserKeys,
      });
      for (const meegoUserInfo of meegoUserInfoList) {
        result.push({
          name: meegoUserInfo.name_cn ?? meegoUserInfo.name_en,
          avatar_url: meegoUserInfo.avatar_url,
          email: meegoUserInfo.email,
          open_id: meegoUserInfo.out_id,
        } as User);
      }
    }

    if (emails.length > 0) {
      const userInfos = (await this.lark.getUserInfoByEmails(emails)) as User[];
      if (userInfos !== undefined && userInfos.length > 0) {
        result.push(...userInfos);
      }
    }

    return result;
  }

  getQueryBitsId(appInfo: BusinessAppInfo) {
    const settingId = appInfo.app_id;
    if (settingId === AppSettingId.LV_ANDROID) {
      return AppSettingId.LV_ANDROID;
    } else if (settingId === AppSettingId.CC_ANDROID) {
      return 2020095699;
    } else if (settingId === AppSettingId.CC_IOS) {
      return 2020095701;
    } else if (settingId === AppSettingId.LV_IOS) {
      return AppSettingId.LV_IOS;
    } else if (
      settingId === AppSettingId.LV_WIN ||
      settingId === AppSettingId.CC_WIN ||
      settingId === AppSettingId.CC_MAC ||
      settingId === AppSettingId.LV_MAC
    ) {
      return AppSettingId.LV_WIN;
    } else if (settingId === AppSettingId.HYPIC_ANDROID || settingId === AppSettingId.RETOUCH_ANDROID) {
      return AppSettingId.RETOUCH_ANDROID;
    } else if (settingId === AppSettingId.HYPIC_IOS || settingId === AppSettingId.RETOUCH_IOS) {
      return AppSettingId.RETOUCH_IOS;
    } else {
      return appInfo.bits_id;
    }
  }

  getQueryVersion(id: AppSettingId, version: string): string {
    if (id === AppSettingId.CC_MAC || id === AppSettingId.CC_WIN) {
      return cc2lvVersion(version);
    }

    if (id === AppSettingId.HYPIC_IOS || id === AppSettingId.HYPIC_ANDROID) {
      return versionUtils.hypic2RetouchVersion(version);
    }

    return version;
  }

  getMeegoBusinessLineLabel(businessLineValue: string): string[] {
    for (const fieldInfo of FaceUMeegoBusinessFieldInfo) {
      const { field_key } = fieldInfo;
      if (field_key !== 'business') {
        continue;
      }
      const { options } = fieldInfo;
      for (const option of options) {
        const { children } = option as any;
        if (children && Object.keys(children).length === 0) {
          // 如果没有 Children，则读取 value
          if (option.value === businessLineValue) {
            const primary_business = option.label;
            return [primary_business, ''];
          }
        }

        if (children && Object.keys(children).length > 0) {
          for (const child of children) {
            if (child.value === businessLineValue) {
              // 找到二级业务线
              const secondary_business = child.label;
              // 再确定一级业务线
              const primary_business = option.label;
              return [primary_business, secondary_business];
            }
          }
        }

        // 如果 Children 里面没有找到，则读取 value
        if (option.value === businessLineValue) {
          const primary_business = option.label;
          return [primary_business, ''];
        }
      }
    }
    return ['', ''];
  }
}
