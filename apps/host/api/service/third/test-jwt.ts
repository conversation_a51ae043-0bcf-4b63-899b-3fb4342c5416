/**
 * 测试JWT token获取功能
 * 这个文件用于验证JWT token获取和使用的实现
 *
 * 使用方法:
 * 1. 只测试服务账号JWT token: runTests()
 * 2. 测试服务账号和用户JWT token: runTests("your.email.prefix", "client_id", "client_secret")
 *
 * 示例:
 * runTests("zhangsan", "your_client_id", "your_client_secret")
 */

import { NetworkX } from '../../utils/NetworkX';

// 服务账号密钥
const SERVICR_ACCOUNT_SECRET = '49a42790851ede0021d05908fdbf0cd2';

/**
 * 测试JWT token获取功能
 */
async function testJwtTokenRetrieval() {
  console.log('开始测试JWT token获取...');

  try {
    // 创建网络请求实例
    const jwtNetwork = new NetworkX('https://cloud.bytedance.net', {
      Authorization: SERVICR_ACCOUNT_SECRET,
    });

    console.log('发送JWT token请求...');
    const response = await jwtNetwork.getWithHead('/auth/api/v1/jwt');

    console.log('响应状态:', response.code);
    console.log('响应头:', response.headers);

    // 检查是否有JWT token
    if (response && response.headers && response.headers['x-jwt-token']) {
      console.log('✅ 成功获取JWT token:', `${response.headers['x-jwt-token'].substring(0, 20)}...`);
      return response.headers['x-jwt-token'];
    } else {
      console.log('❌ 未找到JWT token');
      console.log('完整响应:', JSON.stringify(response, null, 2));
      return null;
    }
  } catch (error: any) {
    console.error('❌ JWT token获取失败:', error);
    return null;
  }
}

/**
 * 测试获取用户JWT token
 */
async function testUserJwtTokenRetrieval(
  serviceJwtToken: string,
  username: string,
  clientId?: string,
  clientSecret?: string,
) {
  console.log('\n开始测试用户JWT token获取...');
  console.log('用户名:', username);

  try {
    // 使用服务账号 JWT token 来获取用户 JWT token
    const jwtNetwork = new NetworkX('https://passport.bytedance.net', {
      'X-Jwt-Token': serviceJwtToken,
      'Content-Type': 'application/json',
    });

    const requestBody = {
      client_id: clientId || 'default_client_id', // 需要配置实际的 client_id
      client_secret: clientSecret || 'default_client_secret', // 需要配置实际的 client_secret
      grant_type: 'authorization_code',
      auth_type: 'custom',
      username, // 个人邮箱前缀
      lark_auth_req: true,
    };

    console.log('发送用户JWT token请求...');
    console.log('请求体:', JSON.stringify(requestBody, null, 2));

    const response = await jwtNetwork.post('/auth/api/v1/token', requestBody);

    console.log('响应状态:', response.code);
    console.log('响应数据:', response.data);

    // 根据实际响应结构调整，这里假设 token 在响应体中
    if (response && response.data && response.data.access_token) {
      console.log('✅ 成功获取用户JWT token:', `${response.data.access_token.substring(0, 20)}...`);
      return response.data.access_token;
    } else {
      console.log('❌ 未找到用户JWT token');
      console.log('完整响应:', JSON.stringify(response, null, 2));
      return null;
    }
  } catch (error: any) {
    console.error('❌ 用户JWT token获取失败:', error);
    return null;
  }
}

/**
 * 测试使用JWT token创建OnCall群组
 */
async function testCreateOnCallGroupWithJwt(jwtToken: string) {
  console.log('\n开始测试使用JWT token创建OnCall群组...');

  try {
    const onCallBackendUrl = 'https://bc-cn-gw.bytedance.net/api/v1/oncall_platform/';
    const network = new NetworkX(onCallBackendUrl, {
      'x-jwt-token': jwtToken,
    });

    console.log('JWT token已设置，准备发送请求...');

    // 这里只是测试网络连接，不会真正创建群组
    // 实际使用时需要传入正确的参数
    console.log('✅ NetworkX实例创建成功，JWT token已配置');
    return true;
  } catch (error: any) {
    console.error('❌ 使用JWT token创建NetworkX实例失败:', error);
    return false;
  }
}

/**
 * 主测试函数
 */
async function runTests(username?: string) {
  console.log('=== JWT Token 功能测试 ===\n');
  const clientId = 'cli_P9ssUS23';
  const clientSecret = 'AV0TGRjtNGzJLDs2HDUNsOhv6QzS7Nkn';
  // 测试1: 获取服务账号JWT token
  const serviceJwtToken = await testJwtTokenRetrieval();

  if (serviceJwtToken) {
    // 测试2: 使用服务账号JWT token
    await testCreateOnCallGroupWithJwt(serviceJwtToken);

    // 测试3: 获取用户JWT token（如果提供了用户名）
    if (username) {
      console.log('\n--- 开始用户JWT token测试 ---');
      const userJwtToken = await testUserJwtTokenRetrieval(serviceJwtToken, username, clientId, clientSecret);

      if (userJwtToken) {
        console.log('✅ 用户JWT token测试成功');
        // 可以在这里添加使用用户JWT token的进一步测试
      } else {
        console.log('❌ 用户JWT token测试失败');
      }
    } else {
      console.log('\n💡 提示: 如果要测试用户JWT token，请提供用户名参数');
      console.log('   例如: runTests("your.email.prefix", "client_id", "client_secret")');
    }
  }

  console.log('\n=== 测试完成 ===');
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runTests('wangleyu').catch(console.error);
}

export { testJwtTokenRetrieval, testUserJwtTokenRetrieval, testCreateOnCallGroupWithJwt, runTests };
